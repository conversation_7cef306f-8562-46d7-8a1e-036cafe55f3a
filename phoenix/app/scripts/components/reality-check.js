/* Lint ------------------- */
/* global documentReady, getCookie, main_params, reality_check */

// Reality Check (RC) - Redirect to main site if RC popup already showed up to player on main site

documentReady(function () {

    if (main_params.loggedin && !main_params.simulated_user) {
        let RCenabled = false,
            RCtimeEnd = 0,
            RCtimeleft = 0;

        // Check RC cookie first before API call
        RCtimeEnd = getCookie('RCETU');

        // If RCETU cookie exists, we check if RC time is up before API call to get RC time
        if (RCtimeEnd) {
            RCenabled = true;
            RCtimeleft = parseInt(RCtimeEnd - new Date());

            // Subtract 5 seconds from RC time to make sure RC Notification won't be missed once user redirected to main site
            RCtimeleft -=5000;

            // If RC time is up, redirect to main site asap
            if (RCtimeleft < 0) {
                window.location.replace(main_params.brand_url);
            }
        }

        // Get RC time from API
        // Use WordPress AJAX endpoint to avoid CORS issues
        if (typeof reality_check !== 'undefined') {
            const formData = new FormData();
            formData.append('action', 'reality_check');
            formData.append('nonce', reality_check.nonce);
            formData.append('rc_action', 'get_next_notification');

            fetch(main_params.ajax_url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                if (result.success && result.data.realityCheck.id && result.data.notification) {
                    RCenabled = true;
                    RCtimeEnd = result.data.notification.endTime;
                    RCtimeleft = result.data.notification.timeLeftInMillis;

                    if (RCenabled) {
                        // Subtract 5 seconds from RC time to make sure RC Notification won't be missed once user redirected to main site
                        RCtimeleft -=5000;

                        // Countdown logic to redirect to main site when RC time is up
                        setInterval(function () {
                            RCtimeleft -= 1000;
                            if (RCtimeleft < 0) {
                                window.location.replace(main_params.brand_url);
                            }
                            if (main_params.debug == "1") {
                                console.log('Reality Check time left:', RCtimeleft / 1000, 'seconds');
                            }
                        }, 1000);

                        if(main_params.debug == "1") {
                            console.log('Reality Check enabled. Time left for RC redirection: ', RCtimeleft / 1000, ' seconds');
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Reality Check API error:', error);
            });
        }
    }
});
